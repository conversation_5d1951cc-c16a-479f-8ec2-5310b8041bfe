import { TRANSLATIONS_ENUM } from '#constants/app'
import { MEDIA_TYPE } from '#constants/media'
import { TRACKING_ACTION } from '#constants/tracking'
import PostCreateNotificationJob from '#jobs/post_create_notification_job'
import ZnCity from '#models/zn_city'
import ZnCountry from '#models/zn_country'
import ZnMedia from '#models/zn_media'
import ZnPost from '#models/zn_post'
import ZnPostCategory from '#models/zn_post_category'
import ZnProductVariant from '#models/zn_product_variant'
import ZnState from '#models/zn_state'
import ZnTracking from '#models/zn_tracking'
import ZnUser from '#models/zn_user'
import ZnUserLikeResource from '#models/zn_user_like_resource'
import ZnVideoTimeline from '#models/zn_video_timeline'
import { AIAgentService } from '#services/gpt/ai_agent_service'
import { GPTTranslationService } from '#services/gpt/translation_service'
import db from '@adonisjs/lucid/services/db'
import queue from '@rlanz/bull-queue/services/main'
import cosineSimilarity from 'compute-cosine-similarity'
import { decode, encode } from 'gpt-tokenizer'
import OpenAI from 'openai'
import { AmazonS3StorageService } from '../../services/aws/s3/aws-s3.service.js'
import { AffiliationService } from './affiliation/affiliation_service.js'
import { GeoService } from './google/geo_service.js'
import { TrackingService } from './tracking_service.js'
import { VideoService } from './video_service.js'
import GenerateThumbnailJob from '#jobs/generate_thumbnail_job'
import RecommendationEngine from './chatbot/recommendation/recommendation_engine.js'
import { htmlToText } from '../../services/commons.js'
import Redis from '@adonisjs/redis/services/main'
import { RESOURCE_TYPE } from '#constants/like_comment_resource'

export class PostService {
  private openai
  private AIService: AIAgentService
  private geoService: GeoService
  private GPTService: GPTTranslationService
  private affiliationService: AffiliationService
  private videoService: VideoService
  private amazonService: AmazonS3StorageService
  private recommendationEngine: RecommendationEngine

  constructor() {
    this.openai = new OpenAI()
    this.AIService = new AIAgentService()
    this.geoService = new GeoService()
    this.GPTService = new GPTTranslationService()
    this.affiliationService = new AffiliationService()
    this.videoService = new VideoService()
    this.amazonService = new AmazonS3StorageService()
    this.recommendationEngine = new RecommendationEngine()
  }

  async determineContent(originPost: ZnPost) {
    // @ts-ignore
    const post = await ZnPost.query({ mode: 'write' })
        .preload('categories')
        .where('id', originPost.id)
        .first() || originPost

    const content = this.cleanText(post.title + ' ' + post.description)
    // const translatedText = await this.GPTService.translationText({
    //   text: content,
    //   translateTo: TRANSLATIONS_ENUM.EN,
    // })

    // Step 1: Get all root-level categories
    const cats = await ZnPostCategory.query().whereNull('parentId')
    const strCat = cats.map((cat) => `- ${cat.name}`).join('\n')

    // Step 2: Build user prompt
    const prompt = `Here is a list of available post categories:
      ${strCat}

      Please analyze the content below and return ONLY ONE most accurate category from the list above. Also, detect the language of the content ("vi" for Vietnamese or "en" for English), and return the price if it is mentioned.

      Content:
      """${content}"""

      Respond in this strict JSON format:
      {"price": number, "category": ["category"], "lang": "vi" | "en"}`

    // Step 3: Use user message and correct model
    const completion = await this.openai.chat.completions.create({
      model: 'o1',
      messages: [
        {
          role: 'user',
          content: prompt,
        },
      ],
      // temperature: 0.1,
    })

    // Step 4: Parse and apply result
    const jsonMatch = completion.choices?.[0]?.message?.content?.match(/{.*}/s)
    if (jsonMatch && jsonMatch[0]) {
      try {
        const { price, category, lang } = JSON.parse(jsonMatch[0])

        if (price && post.price <= 1) post.price = price
        if (lang) post.originLocale = lang

        if (Array.isArray(category) && category.length) {
          const postCats = post.categories.map((cat) => cat.id)
          const selectedCat = await ZnPostCategory.query().whereIn('name', category)
          post.related('categories').sync(selectedCat.map((cat) => cat.id).concat(postCats))
        }

        await post.save()
      } catch (err) {
        console.log(' Failed to parse category result:', err)
      }
    }
  }

  async updatePostEmbedding(postId: string) {
    // @ts-ignore
    const post = await ZnPost.query()
      .preload('store')
      .preload('categories')
      .where('id', postId)
      .first()

    if (post) {
      let text = this.cleanText(`${post.title} ${post.description} at ${post.address} `)

      if (post.categories.length > 0) {
        text += ` in categories:[`
        for (const cat of post.categories) {
          text += ` ${cat.name}, `
        }
        text += ` ]`
      }

      if (post.store) {
        text += ` belong to ${post.store.name} store`
      }
      if (post.tags) {
        text += ` with tags: ${post.tags}`
      }

      if (text) {
        // Trim text to 7500 tokens to avoid exceeding limits
        const tokens = encode(text) // Convert text to tokens
        if (tokens.length > 7500) {
          const tk = tokens.slice(0, 7500) // Trim tokens
          text = decode(tk) // Convert tokens back to readable text
        }

        post.embedding = JSON.stringify(await this.AIService.generateEmbedding(text))

        //update tags
        // const tags = await this.AIService.extractTags(text)
        // post.tags = tags.join(',')

        await post.save()
      }
    }
  }

  cleanText(html: string) {
    return html
      .replace(/<[^>]*>?/gm, '') // Remove HTML tags
      .replace(/[^\w\s]/gi, '') // Remove special characters except spaces
      .replace(/[^\w\sÀ-ỹ]/g, '') // Remove special characters
      .trim() // Trim spaces
  }

  async updatePostLocation(postId: string) {
    // @ts-ignore
    const post = await ZnPost.query({ mode: 'write' }).preload('store').where('id', postId).first()

    // check if post exists, and if post's store exists and has location
    if (!post || !post.store || !post.store.latitude || !post.store.longitude) {
      return
    }

    post.latitude = post.store.latitude
    post.longitude = post.store.longitude

    // check if post has country, state and city
    // if (!post.countryId || !post.stateId || !post.cityId) {
    //   return
    // }

    const lat = Number.parseFloat(post.latitude)
    const lon = Number.parseFloat(post.longitude)

    const address = await this.geoService.getAddressFromLatLon(lat, lon)

    // if (!address.country || !address.state || !address.city) {
    //   return
    // }

    const country = await ZnCountry.findBy({
      name: address.country,
    })

    // if (!country) {
    //   return
    // }

    const state = await ZnState.findBy({
      name: address.state,
      countryId: country?.id,
    })

    // if (!state) {
    //   return
    // }

    const city = await ZnCity.findBy({
      name: address.city,
      stateId: state?.id,
    })

    // if (!city) {
    //   return
    // }

    post.countryId = country?.id || null
    post.stateId = state?.id || null
    post.cityId = city?.id || null

    await post.save()
  }

  async updatePostTags(postId: string) {
    // @ts-ignore
    const post = await ZnPost.query({ mode: 'write' }).preload('store').where('id', postId).first()

    if (post) {
      const text = this.cleanText(`${post.title} ${post.description} at ${post.address} `)
      const tags = await this.AIService.extractTags(text)
      post.tags = tags.join(',')
      await post.save()
    }
  }
  async searchByText(search: string) {
    console.log(`🔍 Starting search with query: "${search}"`)

    const cacheKey = `searchByText:${search.toLowerCase()}`
    console.log(`📋 Cache key generated: "${cacheKey}"`)

    // Check cache
    console.log('🗄️ Checking Redis cache...')
    const cached = await Redis.get(cacheKey)

    if (cached) {
      console.log('✅ Cache hit! Found cached results')
      const parsedCached = JSON.parse(cached)
      const recommendedPostIds = parsedCached.search_post
      console.log(`📊 Cached results contain ${recommendedPostIds?.length || 0} post IDs`)

      const isNonEmptyStringArray = (arr: unknown): arr is string[] =>
        Array.isArray(arr) && arr.length > 0 && arr.every((item) => typeof item === 'string')

      if (isNonEmptyStringArray(recommendedPostIds)) {
        console.log('✅ Cache validation passed, returning cached results')
        return parsedCached.search_post as string[]
      } else {
        console.log('❌ Cache validation failed, proceeding with fresh search')
      }
    } else {
      console.log('❌ Cache miss, proceeding with fresh search')
    }

    const numberOfRecommendations = 100
    console.log(`🎯 Target recommendations: ${numberOfRecommendations}`)

    // Translation step
    console.log('🌐 Starting translation process...')
    console.log(`📝 Original search text: "${search}"`)
    console.time('translationText')

    const translatedText = await this.GPTService.translationText({
      text: search,
      translateTo: TRANSLATIONS_ENUM.EN,
    })

    console.timeEnd('translationText')
    console.log(`📝 Translated text: "${translatedText?.text || 'No translation returned'}"`)

    // Process search text
    const searchText = htmlToText(translatedText?.text) || search
    console.log(`🧹 Processed search text (HTML stripped): "${searchText}"`)

    if (searchText !== search) {
      console.log(
        `🔄 Search text changed after processing (original: "${search}" → processed: "${searchText}")`
      )
    } else {
      console.log('✅ Search text unchanged after processing')
    }

    // Run recommendation engine
    console.log('🚀 Running recommendation engine...')
    console.log('📋 Recommendation engine input:', {
      descriptions: { search_post: searchText },
      maxTotal: numberOfRecommendations,
    })

    console.time('recommendationEngine')
    const selectedPosts = await this.recommendationEngine.run(
      {
        descriptions: { search_post: searchText },
        maxTotal: numberOfRecommendations,
      },
      false
    )
    console.timeEnd('recommendationEngine')

    console.log(
      `📈 Recommendation engine returned ${selectedPosts?.search_post?.length || 0} results`
    )
    console.log('📊 First few post IDs:', selectedPosts?.search_post?.slice(0, 7))

    // Cache results
    console.log('💾 Caching results for 180 seconds...')
    await Redis.setex(cacheKey, 180, JSON.stringify(selectedPosts))
    console.log('✅ Results cached successfully')

    console.log(`🎉 Search completed! Returning ${selectedPosts.search_post?.length || 0} post IDs`)
    return selectedPosts.search_post
  }

  async distributeNotification(post: ZnPost) {
    const store = post?.store
    const lat = post.latitude || store?.latitude
    const lon = post.longitude || store?.longitude
    const batchSize = 500 // Adjust this to control the number of users per job
    let offset = 0
    let hasMoreUsers = true

    while (hasMoreUsers) {
      let users = []

      if (lat && lon && post.source !== 'zurno') {
        // @ts-ignore
        users = await ZnUser.query()
          .whereRaw(
            `((3959 * ACOS(COS(RADIANS(?))
                        * COS(RADIANS(latitude))
                        * COS(RADIANS(longitude) - RADIANS(?)) + SIN(RADIANS(?))
                        * SIN(RADIANS(latitude))) ) <= COALESCE(mile, 200) OR mile is null)`,
            [lat, lon, lat]
          )
          .whereNotNull('lastLoginAt')
          .whereNot('id', post.userId ?? '')
          .offset(offset)
          .limit(batchSize)
      } else {
        users = await ZnUser.query()
          .whereNotNull('lastLoginAt')
          .whereNot('id', post.userId ?? '')
          .offset(offset)
          .limit(batchSize)
      }

      if (users.length === 0) {
        hasMoreUsers = false
      } else {
        await queue.dispatch(
          PostCreateNotificationJob,
          { post, users },
          { queueName: 'notification' }
        )
        offset += batchSize // Move to the next batch
      }
    }
  }

  async isUserLikePost(postId: string, userId?: string): Promise<0 | 1> {
    if (!userId) {
      return 0
    }
    const pivotRecord = await ZnUserLikeResource.query()
      .where({ resourceId: postId, userId, resourceType: RESOURCE_TYPE.POST })
      .first()

    return pivotRecord ? 1 : 0
  }

  async getInteractions(postId: string, actions: TRACKING_ACTION[]) {
    const trackingService = new TrackingService()

    const interactions = await trackingService.getInteractions({
      resourceId: postId,
      actions,
    })

    return interactions
  }

  async searchWithinDistanceInList({
    search,
    lat,
    lon,
    miles,
    allCountry,
    ids,
  }: {
    search: string
    lat: number | undefined
    lon: number | undefined
    miles?: number
    allCountry?: boolean
    ids: string[]
  }): Promise<string[]> {
    if (!lat || !lon || allCountry || ids.length === 0) {
      return []
    }

    const distance = miles || 50
    const postsInDistance = await db
      .from('zn_posts')
      .select(['id', 'embedding', 'storeId', 'latitude', 'longitude'])
      .whereIn('id', ids)
      .whereNotNull('embedding')
      .whereNotNull('latitude')
      .whereNotNull('longitude')
      .whereNull('deletedAt')
      .where('expired', false)
      .where('isUnlist', false)
      .where('isDraft', false)
      .whereRaw(
        '(3959 * acos(cos(radians(?)) * cos(radians(latitude)) * cos(radians(longitude) - radians(?)) + sin(radians(?)) * sin(radians(latitude)))) <= ?',
        [lat, lon, lat, distance]
      )

    if (!postsInDistance.length) return []

    const translatedText = await this.GPTService.translationText({
      text: search,
      translateTo: TRANSLATIONS_ENUM.EN,
    })
    const searchText = translatedText?.text || search
    const queryEmbedding = await this.AIService.generateEmbedding(searchText)

    const stores = await db
      .from('zn_stores')
      .select(['id', 'name'])
      .where('name', 'like', `%${searchText}%`)
    const storeIds = stores.map((store) => store.id)

    let selected = postsInDistance
      .map((post) => ({
        id: post.id,
        similarity: cosineSimilarity(JSON.parse(post.embedding), queryEmbedding),
        storeId: post.storeId,
      }))
      .filter(
        (post) => (post.similarity && post.similarity > 0.78) || storeIds.includes(post.storeId)
      )
      .sort((a, b) => {
        return b.similarity && a.similarity ? b.similarity - a.similarity : 0
      })

    return selected.map((post) => post.id)
  }

  async getPostIdsWithinDistance({
    lat,
    lon,
    miles,
  }: {
    lat: number | undefined
    lon: number | undefined
    miles?: number
  }): Promise<string[]> {
    if (!lat || !lon) return []

    const distance = miles || 50

    const posts = await db
      .from('zn_posts')
      .select('id')
      .whereNotNull('latitude')
      .whereNotNull('longitude')
      .whereRaw(
        '(3959 * acos(cos(radians(?)) * cos(radians(latitude)) * cos(radians(longitude) - radians(?)) + sin(radians(?)) * sin(radians(latitude)))) <= ?',
        [lat, lon, lat, distance]
      )

    return posts.map((p) => p.id)
  }

  async applyMultiSort(
    query: any,
    lat: number | undefined,
    lon: number | undefined,
    sortByMany: string[]
  ) {
    const hasLatLon = lat !== undefined && lon !== undefined

    const useDistanceSort =
      hasLatLon &&
      (sortByMany.includes('distance') ||
        sortByMany.includes('newest') ||
        sortByMany.includes('popular') ||
        sortByMany.includes('default'))

    if (useDistanceSort) {
      query.orderByRaw(
        `(3959 * acos(cos(radians(?)) * cos(radians(latitude)) *
          cos(radians(longitude) - radians(?)) +
          sin(radians(?)) * sin(radians(latitude)))) ASC`,
        [lat, lon, lat]
      )
    }

    for (const sort of sortByMany) {
      if (sort === 'newest') {
        query.orderBy('updatedAt', 'desc')
      } else if (sort === 'popular') {
        query.orderBy('isFavourite', 'desc')
      } else if (sort === 'default') {
        query.orderByRaw('RAND()')
      }
    }
  }

  async searchByTextWithinLocation({
    search,
    lat,
    lon,
    miles,
    allCountry,
  }: {
    search: string
    lat: number
    lon: number
    miles?: number
    allCountry?: boolean
  }): Promise<string[]> {
    if (!lat || !lon || allCountry) {
      return this.searchByText(search)
    }
    const distance = miles || 50
    const distanceInKm = distance * 1.60934
    const translatedText = await this.GPTService.translationText({
      text: search,
      translateTo: TRANSLATIONS_ENUM.EN,
    })
    const searchText = translatedText?.text || search
    const locationConstraint = {
      location: {
        latitude: lat,
        longitude: lon,
        radius: distanceInKm,
      },
    }
    console.log('Location constraint: ', locationConstraint)
    const selectedPostWithinRange = await this.recommendationEngine.run({
      descriptions: { search_post: searchText },
      constraints: {
        search_post: locationConstraint,
      },
    })

    console.log('selectedPostWithinRange: ', selectedPostWithinRange)
    return selectedPostWithinRange.search_post
  }

  async updateOrCreateVideoTimelines(
    postId: string,
    timelines: { id?: string; variantId: string; start: number; end: number }[]
  ) {
    // @ts-ignore
    const post = await ZnPost.query({ mode: 'write' })
      .where({ id: postId })
      .preload('user', (userQuery) => {
        userQuery.preload('affiliate')
      })
      .preload('timelines')
      .first()

    if (!post) {
      return
    }

    for (const timeline of post.timelines) {
      if (!timelines.find((time: any) => time.id == timeline.id)) {
        await timeline.softDelete()
      }
    }

    for (const timeline of timelines) {
      const variant = await ZnProductVariant.find(timeline.variantId)

      let refCodeId
      if (post.userId && variant?.productId) {
        const { commissionPercent, discountPercent } =
          await this.affiliationService.getCommissionAndDiscountPercentage({
            userId: post.userId,
            productId: variant.productId,
          })

        if (commissionPercent > 0 || discountPercent > 0) {
          refCodeId = post.user?.affiliate?.refCodeId
        }
      }

      await ZnVideoTimeline.updateOrCreate(
        { id: timeline.id || '' },
        {
          postId: post.id,
          variantId: timeline.variantId,
          start: timeline.start,
          end: timeline.end,
          refCodeId,
        }
      )
    }
  }

  /**
   * return [{parentResource:"uuid",resourceId:"uuid or shopifyId",count:"number"}]
   */
  async getAddedToCartVariants(postId: string) {
    const addedToCartTracking = await ZnTracking.query()
      .where({
        action: TRACKING_ACTION.ADD_TO_CART,
        parentResourceId: postId,
        parentResource: ZnPost.name,
      })
      .groupBy('parentResource', 'resourceId')
      .select('parentResource', 'resourceId', db.raw('COUNT(*) AS count'))

    const addedToCartIds = addedToCartTracking.map((track) => track.resourceId)
    const cartedVariants = await ZnProductVariant.query()
      .preload('image')
      .preload('product')
      .whereIn('shopifyVariantId', addedToCartIds)
      .orWhereIn('id', addedToCartIds)

    let addedToCart: any = []
    // loop through trackings
    for (const tracking of addedToCartTracking) {
      // find the variant associate with the tracking
      const cartedVariant = cartedVariants.find((vari) =>
        [vari.id, vari.shopifyVariantId].includes(tracking.resourceId)
      )
      if (cartedVariant) {
        // check if the return array has the variant yet
        const addedCartIndex = addedToCart.findIndex(
          (carted: any) => carted.resourceId == cartedVariant.id
        )
        // add to return array if not
        if (addedCartIndex < 0) {
          addedToCart.push({
            ...tracking.serialize(),
            resourceId: cartedVariant.id,
            variant: cartedVariant.serialize(),
          })
        } else {
          // update return array with new carted variant count
          const left = addedToCart.slice(0, addedCartIndex)
          const right = addedToCart.slice(addedCartIndex + 1)

          const addedCart = {
            ...addedToCart[addedCartIndex],
            // @ts-ignore
            count: addedToCart[addedCartIndex].count + tracking.serialize().count,
          }

          addedToCart = [...left, addedCart, ...right]
        }
      }
    }

    return addedToCart
  }

  async generateThumbnailAsync(data: {
    postId: string
    seek?: number | 'random'
    overwrite?: boolean
  }) {
    await queue.dispatch(GenerateThumbnailJob, data)
  }

  async generateThumbnailSync(data: {
    postId: string
    seek?: number | 'random'
    overwrite?: boolean
  }) {
    // @ts-ignore
    const post = await ZnPost.query({ mode: 'write' })
      .preload('thumbnail')
      .preload('medias')
      .where({ id: data.postId })
      .first()

    if (!post) {
      throw Error('Post Not Found')
    }

    // if (post.type != EPostType.LIVE && post.type != EPostType.VIDEO) {
    //   throw Error('Post is not a video post')
    // }

    if (post.thumbnail && !data.overwrite) {
      throw Error('Post Already has thumbnail')
    }

    const inputUrl = post.medias.find((media) => media.type === MEDIA_TYPE.VIDEO)?.url
    if (!inputUrl) {
      throw Error('Cannot get post video link')
    }
    const outputBuffer = await this.videoService.getVideoScreenShotBuffer({
      url: inputUrl,
      seek: data.seek,
    })

    if (!outputBuffer) {
      throw Error('Cannot generate video thumbnail')
    }

    const type = 'image/png'
    var arrayBuffer = new Uint8Array(outputBuffer as any).buffer

    const buffer = Buffer.from(arrayBuffer)
    const transformedFiles = {
      fileName: '',
      buffer: buffer,
      mimeType: type || '',
      extname: '.png',
    }

    console.log('Uploading thumbnail...')

    const upload = await this.amazonService.uploadImages([transformedFiles])

    const mediaDTOs = upload.uploadedFiles.map((file) => {
      return {
        fileKey: file.fileKey,
        url: file.fileKey,
        type: MEDIA_TYPE.IMAGE,
      }
    })

    console.log('Saving thumbnail...')

    const thumbnail = await ZnMedia.create(mediaDTOs[0])

    post.thumbnailId = thumbnail.id
    post.skipSavingUpdatedAt()
    await post.save()

    console.log('Deleting old thumbnail...')

    const oldThumbnail = post.thumbnail
    if (oldThumbnail) {
      await this.amazonService.deleteFile(oldThumbnail.fileKey)
      await oldThumbnail.softDelete()
    }

    return thumbnail
  }

  async transcodeVideoSync(data: { postId: string }) {
    // @ts-ignore
    const post = await ZnPost.query({ mode: 'write' })
      .preload('medias')
      .where({ id: data.postId })
      .first()

    if (!post) {
      throw Error('Post Not Found')
    }

    const inputUrl = post.medias.find((media) => media.type === MEDIA_TYPE.VIDEO)?.url
    if (!inputUrl) {
      throw Error('Cannot get post video link')
    }

    const outputBuffer = await this.videoService.getTranscodedVideoBuffer({
      url: inputUrl,
    })

    if (!outputBuffer) {
      throw Error('Cannot transcode video')
    }

    console.log('Received transcoded video buffer...')

    const type = 'video/mp4'
    var arrayBuffer = new Uint8Array(outputBuffer as any).buffer

    const buffer = Buffer.from(arrayBuffer)
    const transformedFiles = {
      fileName: '',
      buffer: buffer,
      mimeType: type || '',
      extname: '.mp4',
    }

    console.log('Uploading Video...')

    const upload = await this.amazonService.uploadImages([transformedFiles])

    const mediaDTOs = upload.uploadedFiles.map((file) => {
      return {
        fileKey: file.fileKey,
        url: file.fileKey,
        type: MEDIA_TYPE.VIDEO,
      }
    })

    console.log('Saving Video...')

    const video = await ZnMedia.create(mediaDTOs[0])

    await post.related('medias').sync([video.id])

    return video
  }

  /**
   * Persist a draft_post coming from the Post Agent and run
   * the normal enrichment pipeline (category detection, embedding,
   * notifications, etc.).
   *
   * @param draft  — the exact JSON emitted by the Post Agent
   * @param userId — (optional) author of the post
   */
  async createPost(
    draft: {
      title: string
      price?: number
      description: string
    },
    userId?: string
  ): Promise<ZnPost> {
    const createdPost = await ZnPost.create({
      userId,
      title: draft.title?.trim(),
      description: draft.description?.trim(),
      isDraft: false, // agent always submits publish-ready posts
      isUnlist: false,
      expired: false,
      source: null,
    })

    void this.determineContent(createdPost)
    void this.updatePostEmbedding(createdPost.id)
    void this.distributeNotification(createdPost)

    return createdPost.refresh()
  }
}
